/**
 * Test Data Seeding Utility
 *
 * This utility provides functions to seed fake job data for testing purposes.
 * Completely decoupled from production code.
 */

import { APPLY_METHODS } from "../../../lib/apply-methods";
import { CAREER_LEVELS } from "../../../lib/career-levels";
import { CURRENCY_CODES } from "../../../lib/data/currencies";
import {
  JOB_STATUSES,
  VISA_SPONSORSHIP_OPTIONS,
} from "../../../lib/job-status";
import { JOB_TYPES } from "../../../lib/job-types";
import { SALARY_UNITS } from "../../../lib/salary-units";
import { createClient } from "../../../lib/supabase";
import { REMOTE_REGIONS, WORKPLACE_TYPES } from "../../../lib/workplace";

// Constants - extracted from hardcoded values
const SEED_COMPANIES = [
  "TechCorp",
  "Innovate Inc.",
  "Data Systems",
  "Cloud Solutions",
  "QuantumLeap",
] as const;

const SEED_JOB_TITLES = [
  "Software Engineer",
  "Product Manager",
  "Data Scientist",
  "UX Designer",
] as const;

// For monitoring tests, we only use statuses that the monitor checks
const MONITOR_TEST_STATUSES = ["active", "closed", "unknown"] as const;

// Configuration constants
const SEED_CONFIG = {
  // Salary configuration
  baseSalaryMin: 80_000,
  baseSalaryMax: 120_000,
  salaryIncrementMin: 100,
  salaryIncrementMax: 200,

  // Date configuration
  millisecondsPerDay: 24 * 60 * 60 * 1000,
  validThroughDays: 30,

  // Template configuration
  templatePoolSize: 150,

  // Default field values
  defaults: {
    workplace_city: "San Francisco",
    workplace_country: "United States",
    department: "Engineering",
    travel_required: false,
    languages: ["en"],
    skills: "React, Node.js, TypeScript",
    qualifications: "B.S. in Computer Science",
    education_requirements: "Bachelors",
    experience_requirements: "3+ years",
    responsibilities: "Build and maintain web applications.",
    featured: false,
    industry: "Technology",
    occupational_category: "Software Development",
    processing_status: "completed",
    source_type: "manual",
    source_name: "Bordfeed-Stress-Test",
    ai_cost: 0.001,
    ai_tokens: 3500,
    ai_model: "gpt-4o-mini",
    timezone_requirements: "Pacific Time (PT) preferred",
    benefits:
      "Health insurance, dental, vision, 401k match, flexible PTO, remote work stipend",
    application_requirements: "Please include portfolio and cover letter",
    job_source_platform: "LinkedIn Jobs",
  },
} as const;

interface FakeJob {
  sourced_at: string;
  source_url: string;
  title: string;
  _company: string;
  type: string;
  description: string;
  apply_url: string;
  apply_method: string;
  posted_date: string;
  status: string;
  salary_min: number;
  salary_max: number;
  salary_currency: string;
  salary_unit: string;
  workplace_type: string;
  remote_region: string | null;
  timezone_requirements: string | null;
  workplace_city: string | null;
  workplace_country: string;
  benefits: string | null;
  application_requirements: string | null;
  valid_through: string;
  job_identifier: string;
  job_source_name: string;
  department: string;
  travel_required: boolean;
  career_level: string[];
  visa_sponsorship: string;
  languages: string[];
  skills: string;
  qualifications: string;
  education_requirements: string;
  experience_requirements: string;
  responsibilities: string;
  featured: boolean;
  industry: string;
  occupational_category: string;
  processing_status: string;
  source_type: string;
  source_name: string;
  dedupe_key: string;
  raw_sourced_job_data: Record<string, unknown>;
  ai_metadata: Record<string, unknown>;
}

// Helper functions - extracted from repetitive logic
function generateHTMLContent(title: string, content: string): string {
  return `<html><head><title>${title}</title></head><body>${content}</body></html>`;
}

function generateDataURL(htmlContent: string): string {
  return `data:text/html,${encodeURIComponent(htmlContent)}`;
}

function generateSourceURL(
  status: string,
  title: string,
  _company: string,
  description: string,
  id: number
): string {
  switch (status) {
    case "closed": {
      // Test phrase matcher with data URL
      const closedContent =
        "<h1>Job No Longer Available</h1><p>This position has been filled.</p>";
      return generateDataURL(generateHTMLContent(title, closedContent));
    }

    case "unknown":
      // Test HEAD checker with a 404
      return `https://httpbin.org/status/404?job_id=${id}`;

    default: {
      // Test AI checker with a neutral data URL
      const activeContent = `<h1>${title}</h1><p>${description}</p>`;
      return generateDataURL(generateHTMLContent(title, activeContent));
    }
  }
}

function calculateSalary(index: number): { min: number; max: number } {
  return {
    min: SEED_CONFIG.baseSalaryMin + index * SEED_CONFIG.salaryIncrementMin,
    max: SEED_CONFIG.baseSalaryMax + index * SEED_CONFIG.salaryIncrementMax,
  };
}

function generatePostedDate(index: number): string {
  return new Date(
    Date.now() - index * SEED_CONFIG.millisecondsPerDay
  ).toISOString();
}

function generateValidThroughDate(): string {
  return new Date(
    Date.now() + SEED_CONFIG.validThroughDays * SEED_CONFIG.millisecondsPerDay
  ).toISOString();
}

function generateWorkplaceSettings(index: number): {
  workplace_type: string;
  remote_region: string | null;
} {
  const isRemote = index % 2 === 0;
  return {
    workplace_type: isRemote ? WORKPLACE_TYPES[2] : WORKPLACE_TYPES[0], // "Remote" : "On-site"
    remote_region: isRemote ? REMOTE_REGIONS[4] : null, // "US Only" : null
  };
}

function generateDedupeKey(
  _company: string,
  title: string,
  sourceUrl: string
): string {
  return `${_company}|${title}|${sourceUrl.slice(0, 100)}`;
}

function createAIMetadata(): Record<string, unknown> {
  return {
    cost: { total: SEED_CONFIG.defaults.ai_cost },
    tokens: { total: SEED_CONFIG.defaults.ai_tokens },
    model: SEED_CONFIG.defaults.ai_model,
  };
}

// New helper functions for comprehensive field coverage
function generateTimezoneRequirements(index: number): string | null {
  const timezones = [
    "Pacific Time (PT) preferred",
    "Eastern Time (ET) required",
    "Central European Time (CET) preferred",
    "UTC+0 to UTC+8 acceptable",
    null, // Some jobs don't have timezone requirements
  ];
  return timezones[index % timezones.length];
}

function generateBenefits(index: number): string | null {
  const benefitOptions = [
    "Health insurance, dental, vision, 401k match, flexible PTO, remote work stipend",
    "Comprehensive medical coverage, stock options, professional development budget, gym membership",
    "Health & wellness package, unlimited PTO, learning stipend, home office setup",
    "Medical/dental/vision, retirement plan, flexible hours, company equity",
    null, // Some jobs don't list benefits
  ];
  return benefitOptions[index % benefitOptions.length];
}

function generateApplicationRequirements(index: number): string | null {
  const requirements = [
    "Please include portfolio and cover letter",
    "Submit resume with 2-3 professional references",
    "Include GitHub profile and code samples",
    "Portfolio required - showcase 3 recent projects",
    null, // Some jobs have standard application process
  ];
  return requirements[index % requirements.length];
}

function generateJobIdentifier(index: number): string {
  const prefixes = ["JOB", "REQ", "POS", "ROLE", "OPEN"];
  const prefix = prefixes[index % prefixes.length];
  return `${prefix}-${(index + 1000).toString().padStart(6, "0")}`;
}

function generateJobSourceName(index: number): string {
  const sources = [
    "LinkedIn Jobs",
    "Indeed",
    "AngelList",
    "Stack Overflow Jobs",
    "Company Website",
  ];
  return sources[index % sources.length];
}

// Template generator function
function generateJobTemplate(index: number): Omit<FakeJob, "sourced_at"> {
  const jobTypeIndex = index % SEED_JOB_TITLES.length;
  const statusIndex = index % MONITOR_TEST_STATUSES.length;
  const companyIndex = index % SEED_COMPANIES.length;

  const _company = SEED_COMPANIES[companyIndex];
  const title = SEED_JOB_TITLES[jobTypeIndex];
  const status = MONITOR_TEST_STATUSES[statusIndex];
  const id = index + 1;

  const description = `Description for ${title} at ${_company}. Status is ${status}.`;
  const source_url = generateSourceURL(
    status,
    title,
    _company,
    description,
    id
  );
  const salary = calculateSalary(index);
  const workplace = generateWorkplaceSettings(index);

  return {
    source_url,
    title: `${title} #${id}`,
    _company,
    type: JOB_TYPES[0], // "Full-time"
    description,
    apply_url: `https://example.com/apply/${id}`,
    apply_method: APPLY_METHODS[0], // "link"
    posted_date: generatePostedDate(index),
    status: JOB_STATUSES[1], // "active" - All jobs start as active for the monitor
    salary_min: salary.min,
    salary_max: salary.max,
    salary_currency: CURRENCY_CODES[0], // "USD"
    salary_unit: SALARY_UNITS[4], // "year"
    workplace_type: workplace.workplace_type,
    remote_region: workplace.remote_region,
    timezone_requirements: generateTimezoneRequirements(index),
    workplace_city: SEED_CONFIG.defaults.workplace_city,
    workplace_country: SEED_CONFIG.defaults.workplace_country,
    benefits: generateBenefits(index),
    application_requirements: generateApplicationRequirements(index),
    valid_through: generateValidThroughDate(),
    job_identifier: generateJobIdentifier(index),
    job_source_name: generateJobSourceName(index),
    department: SEED_CONFIG.defaults.department,
    travel_required: SEED_CONFIG.defaults.travel_required,
    career_level: [CAREER_LEVELS[4]], // ["Mid Level"]
    visa_sponsorship: VISA_SPONSORSHIP_OPTIONS[2], // "Not specified"
    languages: [...SEED_CONFIG.defaults.languages],
    skills: SEED_CONFIG.defaults.skills,
    qualifications: SEED_CONFIG.defaults.qualifications,
    education_requirements: SEED_CONFIG.defaults.education_requirements,
    experience_requirements: SEED_CONFIG.defaults.experience_requirements,
    responsibilities: SEED_CONFIG.defaults.responsibilities,
    featured: SEED_CONFIG.defaults.featured,
    industry: SEED_CONFIG.defaults.industry,
    occupational_category: SEED_CONFIG.defaults.occupational_category,
    processing_status: SEED_CONFIG.defaults.processing_status,
    source_type: SEED_CONFIG.defaults.source_type,
    source_name: SEED_CONFIG.defaults.source_name,
    dedupe_key: generateDedupeKey(_company, `${title} #${id}`, source_url),
    raw_sourced_job_data: { raw_content: "...html content..." },
    ai_metadata: createAIMetadata(),
  };
}

// Generate the template pool
const FAKE_JOBS: Omit<FakeJob, "sourced_at">[] = Array.from(
  { length: SEED_CONFIG.templatePoolSize },
  (_, index) => generateJobTemplate(index)
);

/**
 * Seed fake job data for testing
 * @param count Number of jobs to create (1-1000)
 * @param cleanSlate Whether to clean existing stress test data first
 * @returns Promise with result data
 */
export async function seedFakeJobs(
  count: number = 6,
  cleanSlate: boolean = true
) {
  if (count <= 0 || count > 1000) {
    throw new Error("Count must be between 1 and 1000");
  }

  const supabase = createClient();

  // Truncate only stress test data for a clean slate
  if (cleanSlate) {
    const { data: jobsToDelete } = await supabase
      .from("jobs")
      .select("id")
      .eq("source_name", "Bordfeed-Stress-Test");

    if (jobsToDelete && jobsToDelete.length > 0) {
      const jobIds = jobsToDelete.map((j) => j.id);
      await supabase.from("job_monitor_logs").delete().in("job_id", jobIds);
      await supabase.from("jobs").delete().in("id", jobIds);
    }
  }

  const jobsToInsert: FakeJob[] = [];
  for (let i = 0; i < count; i++) {
    const template = FAKE_JOBS[i % FAKE_JOBS.length];
    const newJob = {
      ...template,
      source_name: i % 2 === 0 ? "Bordfeed-Stress-Test" : "Organic-TestSource", // Mix in other sources
      sourced_at: new Date().toISOString(),
      // Make key unique to avoid dedupe conflicts during stress test
      dedupe_key: `${template._company}|${
        template.title
      }|${template.source_url.slice(0, 50)}|${Date.now()}|${i}`,
      source_url: template.source_url.includes("?")
        ? `${template.source_url}&i=${i}`
        : template.source_url,
    };
    jobsToInsert.push(newJob);
  }

  const { data, error } = await supabase
    .from("jobs")
    .insert(jobsToInsert)
    .select();

  if (error) {
    throw error;
  }

  return {
    message: `${data.length} fake jobs seeded successfully.`,
    count: data.length,
    data,
  };
}
