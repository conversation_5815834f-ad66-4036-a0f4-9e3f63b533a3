/**
 * Basic smoke tests for Health store
 *
 * These tests verify that the health store auto-refresh functionality
 * and data fetching work correctly.
 */

// @ts-expect-error - Testing library types not available in production build
import { act, renderHook } from "@testing-library/react";
// @ts-expect-error - Vitest types not available in production build
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import { useHealthStore } from "../../../lib/stores/health.store";

// Mock fetch globally
global.fetch = vi.fn() as unknown as typeof fetch;

// Mock timers
vi.useFakeTimers();

describe("HealthStore Smoke Tests", () => {
  beforeEach(() => {
    // Reset store state before each test
    useHealthStore.setState({
      data: {
        systemHealth: undefined,
        sourcesHealth: undefined,
        connectedSourcesHealth: undefined,
        jobStats: undefined,
        monitoringData: undefined,
        logsData: undefined,
        deduplicationData: undefined,
        lastUpdated: new Date().toISOString(),
      },
      autoRefresh: false,
      refreshInterval: 30_000,
      loading: false,
      error: null,
    });

    // Reset fetch mock
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllTimers();
  });

  it("should initialize with default state", () => {
    const { result } = renderHook(() => useHealthStore());

    expect(result.current.systemHealth).toBe(null);
    expect(result.current.sourcesHealth).toBe(null);
    expect(result.current.jobStats).toBe(null);
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBe(null);
    expect(result.current.autoRefreshInterval).toBe(null);
  });

  it("should handle fetchAllHealthData without crashing", async () => {
    // Mock all health endpoints
    const mockResponses = [
      { ok: true, json: async () => ({ status: "healthy", checks: {} }) },
      { ok: true, json: async () => ({ sources: [] }) },
      { ok: true, json: async () => ({ total: 100, pending: 5 }) },
      { ok: true, json: async () => ({ total_duplicates: 10 }) },
    ];

    (global.fetch as ReturnType<typeof vi.fn>)
      .mockResolvedValueOnce(mockResponses[0])
      .mockResolvedValueOnce(mockResponses[1])
      .mockResolvedValueOnce(mockResponses[2])
      .mockResolvedValueOnce(mockResponses[3]);

    const { result } = renderHook(() => useHealthStore());

    // Test fetchAllHealthData action
    await act(async () => {
      await result.current.actions.fetchAllHealthData();
    });

    // Verify state was updated
    expect(result.current.systemHealth).toBeTruthy();
    expect(result.current.sourcesHealth).toBeTruthy();
    expect(result.current.jobStats).toBeTruthy();
    expect(result.current.deduplicationData).toBeTruthy();
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBe(null);
    expect(result.current.lastUpdated).toBeTruthy();
  });

  it("should handle fetch errors gracefully", async () => {
    (global.fetch as ReturnType<typeof vi.fn>).mockRejectedValueOnce(
      new Error("Network error")
    );

    const { result } = renderHook(() => useHealthStore());

    await act(async () => {
      await result.current.actions.fetchSystemHealth();
    });

    // Verify error state
    expect(result.current.error).toBe("Network error");
    expect(result.current.loading).toBe(false);
  });

  it("should handle auto-refresh setup and cleanup", () => {
    const { result } = renderHook(() => useHealthStore());

    // Enable auto-refresh
    act(() => {
      result.current.actions.enableAutoRefresh(5000); // 5 seconds
    });

    // Verify interval was set
    expect(result.current.autoRefreshInterval).toBeTruthy();

    // Disable auto-refresh
    act(() => {
      result.current.actions.disableAutoRefresh();
    });

    // Verify interval was cleared
    expect(result.current.autoRefreshInterval).toBe(null);
  });

  it("should handle refresh action without crashing", async () => {
    // Mock successful responses
    const mockResponses = [
      { ok: true, json: async () => ({ status: "healthy" }) },
      { ok: true, json: async () => ({ sources: [] }) },
      { ok: true, json: async () => ({ total: 100 }) },
      { ok: true, json: async () => ({ total_duplicates: 10 }) },
    ];

    (global.fetch as ReturnType<typeof vi.fn>)
      .mockResolvedValueOnce(mockResponses[0])
      .mockResolvedValueOnce(mockResponses[1])
      .mockResolvedValueOnce(mockResponses[2])
      .mockResolvedValueOnce(mockResponses[3]);

    const { result } = renderHook(() => useHealthStore());

    // Test refresh action
    await act(async () => {
      await result.current.actions.refresh();
    });

    // Verify refresh completed without errors
    expect(result.current.loading).toBe(false);
    expect(result.current.lastUpdated).toBeTruthy();
  });

  it("should handle setRefreshInterval without crashing", () => {
    const { result } = renderHook(() => useHealthStore());

    // Test setting different intervals
    act(() => {
      result.current.actions.setRefreshInterval(10_000); // 10 seconds
    });

    act(() => {
      result.current.actions.setRefreshInterval(30_000); // 30 seconds
    });

    // Should not crash and interval should be updated
    expect(result.current.autoRefreshInterval).toBeTruthy();
  });
});
