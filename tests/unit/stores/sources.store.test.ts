/**
 * Basic smoke tests for Sources store
 *
 * These tests verify that critical store actions work without errors
 * and maintain proper state consistency.
 */

// @ts-expect-error - Testing library types not available in production build
import { act, renderHook } from "@testing-library/react";
// @ts-expect-error - Vitest types not available in production build
import { beforeEach, describe, expect, it, vi } from "vitest";
import { useSourcesStore } from "../../../lib/stores/sources.store";

// Mock fetch globally
global.fetch = vi.fn() as unknown as typeof fetch;

describe("SourcesStore Smoke Tests", () => {
  beforeEach(() => {
    // Reset store state before each test
    useSourcesStore.setState({
      sources: [],
      loading: false,
      error: null,
      lastFetch: null,
    });

    // Reset fetch mock
    vi.clearAllMocks();
  });

  it("should initialize with default state", () => {
    const { result } = renderHook(() => useSourcesStore());

    expect(result.current.sources).toEqual([]);
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBe(null);
    expect(result.current.lastSync).toBe(null);
    expect(result.current.activeRuns).toBeInstanceOf(Set);
    expect(result.current.activeRuns.size).toBe(0);
  });

  it("should handle fetch action without crashing", async () => {
    const mockResponse = {
      ok: true,
      json: async () => ({
        success: true,
        sources: [{ id: "1", name: "Test Source", status: "active" }],
      }),
    };

    (global.fetch as ReturnType<typeof vi.fn>).mockResolvedValueOnce(
      mockResponse
    );

    const { result } = renderHook(() => useSourcesStore());

    // Test fetch action
    await act(async () => {
      await result.current.actions.fetch();
    });

    // Verify state was updated
    expect(result.current.sources).toHaveLength(1);
    expect(result.current.sources[0].name).toBe("Test Source");
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBe(null);
  });

  it("should handle fetch errors gracefully", async () => {
    (global.fetch as ReturnType<typeof vi.fn>).mockRejectedValueOnce(
      new Error("Network error")
    );

    const { result } = renderHook(() => useSourcesStore());

    await act(async () => {
      await result.current.actions.fetch();
    });

    // Verify error state
    expect(result.current.error).toBe("Network error");
    expect(result.current.loading).toBe(false);
    expect(result.current.sources).toEqual([]);
  });

  it("should handle update action without crashing", () => {
    const { result } = renderHook(() => useSourcesStore());

    // Set initial state with a source
    act(() => {
      useSourcesStore.setState({
        sources: [
          {
            id: "1",
            name: "Original",
            description: "Test source",
            enabled: true,
            actor_id: "test-actor",
            stats: {
              last_fetch_at: null,
              last_run_status: null,
              last_error: null,
            },
            status: "active",
            total_jobs: 0,
            success_rate: 0,
            schedule: {
              id: null,
              name: null,
              title: null,
              cronExpression: null,
              timezone: null,
              isEnabled: false,
              nextRunAt: null,
              lastRunAt: null,
              apifyUrl: null,
            },
            config: {
              apify_actor_url: "test-url",
            },
          },
        ],
      });
    });

    // Test update action
    act(() => {
      result.current.actions.update("1", { name: "Updated" });
    });

    // Verify update
    expect(result.current.sources[0].name).toBe("Updated");
    expect(result.current.sources[0].status).toBe("active"); // Should preserve other fields
  });

  it("should track active runs correctly", async () => {
    const mockResponse = { ok: true };
    (global.fetch as ReturnType<typeof vi.fn>).mockResolvedValueOnce(
      mockResponse
    );

    const { result } = renderHook(() => useSourcesStore());

    // Set initial state with a source
    act(() => {
      useSourcesStore.setState({
        sources: [
          {
            id: "1",
            name: "Test Source",
            description: "Test source",
            enabled: true,
            actor_id: "test-actor",
            stats: {
              last_fetch_at: null,
              last_run_status: null,
              last_error: null,
            },
            status: "inactive",
            total_jobs: 0,
            success_rate: 0,
            schedule: {
              id: null,
              name: null,
              title: null,
              cronExpression: null,
              timezone: null,
              isEnabled: false,
              nextRunAt: null,
              lastRunAt: null,
              apifyUrl: null,
            },
            config: {
              apify_actor_url: "test-url",
            },
          },
        ],
      });
    });

    // Test runSource action
    await act(async () => {
      await result.current.actions.runSource("1");
    });

    // Verify active run tracking and optimistic update
    expect(result.current.activeRuns.has("1")).toBe(true);
    expect(result.current.sources[0].status).toBe("running");
  });
});
